defmodule MqttableWeb.SendMessageModalComponent do
  @moduledoc """
  A reusable modal component for sending MQTT messages.

  This component provides a form for composing and sending MQTT messages with support for:
  - Client selection with automatic fallback
  - Form state persistence across modal open/close cycles
  - MQTT 5.0 properties and user properties
  - Click-outside-to-close functionality
  """

  use MqttableWeb, :live_component
  import MqttableWeb.Shared.MessageFormComponents

  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:show_modal, fn -> false end)
      |> assign_new(:publish_form, fn -> default_publish_form() end)
      |> assign_new(:alert_message, fn -> nil end)
      |> assign_new(:alert_type, fn -> nil end)
      |> assign_new(:uploaded_file, fn -> nil end)
      |> allow_upload(:file, accept: :any, max_entries: 1, max_file_size: 16_000_000)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    Logger.debug(
      "SendMessageModalComponent update called with assigns keys: #{inspect(Map.keys(assigns))}"
    )

    # If modal is open and this update is not related to modal state changes,
    # skip the update to prevent disrupting user interaction
    if socket.assigns[:show_modal] && assigns[:show_modal] &&
         not Map.has_key?(assigns, :form_state) &&
         socket.assigns[:active_broker_name] == assigns[:active_broker_name] do
      Logger.debug(
        "Modal is open and update is not form-related, skipping to preserve user input"
      )

      {:ok, socket}
    else
      # Store the broker name when modal is shown, so we can use it when closing
      current_broker_name =
        if assigns[:show_modal] && assigns[:active_broker_name] do
          assigns[:active_broker_name]
        else
          socket.assigns[:stored_broker_name]
        end

      # Use form state from parent if provided, otherwise use current or default
      current_form =
        assigns[:form_state] ||
          socket.assigns[:publish_form] ||
          default_publish_form()

      Logger.debug("Current form state: #{inspect(current_form)}")

      # Smart client selection logic
      updated_form =
        if assigns[:show_modal] do
          smart_client_selection(current_form, assigns[:active_broker_name])
        else
          current_form
        end

      Logger.debug("After smart client selection: #{inspect(updated_form)}")

      # If smart client selection changed the client_id, save the updated form state
      if updated_form != current_form && assigns[:show_modal] do
        Logger.debug("Smart client selection changed form, saving updated state")
        cleaned_form = clean_form_for_storage(updated_form)
        send(self(), {:update_send_modal_form, cleaned_form, current_broker_name})
      end

      # Load MQTT 5.0 properties collapse state from ui_state
      mqtt5_collapsed = get_mqtt5_properties_collapsed_state(assigns[:active_broker_name])

      # Get connected clients for the active broker
      connected_clients = get_connected_clients(assigns[:active_broker_name] || "")

      # Simplified form update - no complex template logic needed

      socket =
        socket
        |> assign(assigns)
        |> assign(:publish_form, updated_form)
        |> assign(:mqtt5_properties_collapsed, mqtt5_collapsed)
        |> assign(:stored_broker_name, current_broker_name)
        |> assign(:connected_clients, connected_clients)

      {:ok, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <!-- Send Message Modal with Sidebar Layout -->
      <dialog
        id="send-message-modal"
        class={"modal #{if @show_modal, do: "modal-open", else: ""}"}
        style={if @show_modal, do: "", else: "display: none;"}
      >
        <div class="modal-backdrop" phx-click="close_send_modal"></div>
        <div
          class="modal-box max-w-6xl ml-auto mr-6 mt-6 mb-6 h-[calc(100vh-3rem)] flex flex-col send-message-modal-sidebar"
          id="send-message-modal-content"
        >
          <!-- Modal Header -->
          <div class="flex items-center justify-between mb-4 flex-shrink-0">
            <h3 class="text-lg font-semibold flex items-center">
              <.icon name="hero-paper-airplane" class="size-5 mr-2" /> Send MQTT Message
            </h3>
            <button class="btn btn-sm btn-circle btn-ghost" phx-click="close_send_modal">
              ✕
            </button>
          </div>
          
    <!-- Alert Message -->
          <div :if={@alert_message} class="mb-4 flex-shrink-0">
            <div
              role="alert"
              class={[
                "alert",
                @alert_type == :success && "alert-success",
                @alert_type == :error && "alert-error",
                @alert_type == :warning && "alert-warning"
              ]}
            >
              <svg
                :if={@alert_type == :success}
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 shrink-0 stroke-current"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <svg
                :if={@alert_type == :error}
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 shrink-0 stroke-current"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <svg
                :if={@alert_type == :warning}
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 shrink-0 stroke-current"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              <span>{@alert_message}</span>
              <button
                type="button"
                class="btn btn-sm btn-ghost ml-auto"
                phx-click="dismiss_alert"
                phx-target={@myself}
              >
                ✕
              </button>
            </div>
          </div>
          
    <!-- Modal Content with Sidebar Layout -->
          <div
            class="flex-1 overflow-hidden"
            phx-hook="PayloadEditor"
            id={"send-payload-container-#{@myself}"}
          >
            <div class="h-full flex gap-6">
              <!-- Left Column: Main Form (60%) -->
              <div class="flex-1 overflow-y-auto pr-2">
                <.form
                  for={%{}}
                  as={:publish}
                  phx-submit="send_message"
                  phx-change="form_changed"
                  phx-target={@myself}
                  class="space-y-4"
                  id="publish-form"
                >
                  <!-- Client Selection -->
                  <.client_selection
                    form={@publish_form}
                    connected_clients={@connected_clients}
                    active_broker_name={@active_broker_name}
                    myself={@myself}
                    label="Client"
                  />
                  
    <!-- Topic Input -->
                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Topic</span>
                    </label>
                    <label class="input input-bordered flex items-center gap-2">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="14538"
                      >
                        <path
                          d="M512 85.333333c235.648 0 426.666667 191.018667 426.666667 426.666667s-191.018667 426.666667-426.666667 426.666667S85.333333 747.648 85.333333 512 276.352 85.333333 512 85.333333z m0 85.333334C323.477333 170.666667 170.666667 323.477333 170.666667 512s152.810667 341.333333 341.333333 341.333333 341.333333-152.810667 341.333333-341.333333S700.522667 170.666667 512 170.666667z m0 128c23.552 0 42.666667 19.114667 42.666667 42.666666v128h128c23.552 0 42.666667 19.114667 42.666667 42.666667s-19.114667 42.666667-42.666667 42.666667h-128v128c0 23.552-19.114667 42.666667-42.666667 42.666667s-42.666667-19.114667-42.666667-42.666667v-128h-128c-23.552 0-42.666667-19.114667-42.666667-42.666667s19.114667-42.666667 42.666667-42.666667h128v-128c0-23.552 19.114667-42.666667 42.666667-42.666667z"
                          fill="#172B4D"
                          p-id="14539"
                        >
                        </path>
                      </svg>
                      <input
                        type="text"
                        name="topic"
                        value={@publish_form["topic"]}
                        placeholder="Topic (e.g., 'device/sensor/temperature')"
                        class="grow"
                      />
                    </label>
                  </div>
                  
    <!-- Payload Input with File Upload -->
                  <.payload_input_with_file_upload
                    form={@publish_form}
                    myself={@myself}
                    uploads={@uploads}
                    uploaded_file={@uploaded_file}
                    label="Payload"
                  />
                  
    <!-- QoS Selection -->
                  <.qos_selection form={@publish_form} myself={@myself} label="QoS Level" />
                  
    <!-- Retain Checkbox -->
                  <.retain_checkbox form={@publish_form} label="Retain Message" />
                  
    <!-- MQTT 5.0 Properties Section -->
                  <.mqtt5_properties_section
                    form={@publish_form}
                    myself={@myself}
                    collapsed={@mqtt5_properties_collapsed}
                    show_properties={
                      show_mqtt5_properties?(@publish_form["client_id"], @active_broker_name)
                    }
                  />
                  
    <!-- Submit Button -->
                  <div class="form-control w-full">
                    <button type="submit" class="btn btn-primary">
                      <.icon name="hero-paper-airplane" class="size-4 mr-2" /> Send Message
                    </button>
                  </div>
                </.form>
              </div>
              
    <!-- Right Column: Template Helper Sidebar (40%) -->
              <div class="w-2/5 border-l border-base-300 pl-6 overflow-y-auto">
                <.live_component
                  module={MqttableWeb.TwoTabTemplateHelperComponent}
                  id={"two-tab-template-helper-#{@myself}"}
                  target_textarea_id={"enhanced-payload-editor-send-#{@myself}"}
                  payload={@publish_form["payload"] || ""}
                  payload_format={@publish_form["payload_format"] || "text"}
                  active_broker_name={@active_broker_name}
                />
              </div>
            </div>
          </div>
        </div>
      </dialog>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("form_changed", params, socket) do
    require Logger
    Logger.debug("SendMessageModalComponent: form_changed event received")
    # Extract form parameters from the publish namespace
    publish_params = params["publish"] || params
    # Update form state with all current values from the form
    updated_form = update_form_with_params(socket.assigns.publish_form, publish_params)
    # Validate payload based on current format
    validated_form = validate_payload_in_form(updated_form)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(validated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})

    {:noreply, assign(socket, :publish_form, validated_form)}
  end

  @impl true
  def handle_event("user_property_changed", params, socket) do
    # Extract user properties from the form parameters
    current_properties = socket.assigns.publish_form["user_properties"] || []

    # Parse the user property fields from params
    updated_properties = parse_user_properties_from_params(params, current_properties)

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", updated_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    current_properties = socket.assigns.publish_form["user_properties"] || []
    new_properties = current_properties ++ [%{"key" => "", "value" => ""}]

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("remove_user_property", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    current_properties = socket.assigns.publish_form["user_properties"] || []

    new_properties = List.delete_at(current_properties, index)

    # Preserve all existing form fields, only update user_properties
    updated_form = Map.put(socket.assigns.publish_form, "user_properties", new_properties)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})
    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("toggle_mqtt5_properties", _params, socket) do
    broker_name = socket.assigns[:active_broker_name]
    current_collapsed = socket.assigns[:mqtt5_properties_collapsed] || false
    new_collapsed = !current_collapsed

    # Save the collapse state to ui_state
    if broker_name do
      save_mqtt5_properties_collapsed_state(broker_name, new_collapsed)
    end

    {:noreply, assign(socket, :mqtt5_properties_collapsed, new_collapsed)}
  end

  @impl true
  def handle_event("send_message", params, socket) do
    require Logger
    Logger.debug("SendMessageModalComponent: Received send_message event")
    Logger.debug("SendMessageModalComponent: Full params: #{inspect(params)}")

    # Extract form parameters from the publish namespace
    publish_params = params["publish"] || params
    client_id = publish_params["client_id"]
    topic = publish_params["topic"]

    # Get payload and format from component state instead of form params
    payload = socket.assigns.publish_form["payload"] || ""
    payload_format = socket.assigns.publish_form["payload_format"] || "text"

    Logger.debug(
      "SendMessageModalComponent: Extracted - client_id: #{inspect(client_id)}, topic: #{inspect(topic)}, payload length: #{String.length(payload || "")}, format: #{payload_format}"
    )

    qos = String.to_integer(publish_params["qos"] || "0")
    retain = publish_params["retain"] == "on"

    # Process payload with new simplified logic
    {final_payload, payload_error} = process_payload(payload, socket.assigns[:active_broker_name])

    # Update form state with all current values (including MQTT 5.0 properties)
    updated_form = update_form_with_params(socket.assigns.publish_form, publish_params)

    # Validate payload AFTER template evaluation
    validated_form =
      validate_payload_in_form_after_template(updated_form, final_payload, payload_format)

    socket = assign(socket, :publish_form, validated_form)

    # Validate required fields and payload format
    if client_id != "" && topic != "" && validated_form["payload_validation_error"] == nil &&
         payload_error == nil do
      # Use final payload (either template-generated or manual)
      actual_payload = if payload_error == nil, do: final_payload, else: payload

      # Encode payload based on format
      case encode_payload_for_transmission(actual_payload, payload_format) do
        {:ok, encoded_payload} ->
          # Build MQTT 5.0 properties if the client supports it
          connected_clients = get_connected_clients(socket.assigns[:active_broker_name] || "")
          properties = build_mqtt5_publish_properties(params, connected_clients, client_id)

          # Prepare publish options
          publish_opts = [qos: qos, retain: retain]

          # Add properties if any
          publish_opts =
            if map_size(properties) > 0 do
              [{:properties, properties} | publish_opts]
            else
              publish_opts
            end

          # Attempt to publish the message with encoded payload
          case Mqttable.MqttClient.Manager.publish(
                 client_id,
                 topic,
                 encoded_payload,
                 publish_opts
               ) do
            {:ok, packet_id} ->
              # Success - show success alert in modal and keep modal open
              {message, _flash_type} = format_publish_result(packet_id)
              timestamped_message = add_timestamp_to_message(message)

              socket =
                socket
                |> assign(:alert_message, timestamped_message)
                |> assign(:alert_type, :success)

              {:noreply, socket}

            {:error, :not_connected} ->
              timestamped_message = add_timestamp_to_message("Client is not connected")

              socket =
                socket
                |> assign(:alert_message, timestamped_message)
                |> assign(:alert_type, :error)

              {:noreply, socket}

            {:error, _reason, error_message} ->
              timestamped_message =
                add_timestamp_to_message("Failed to send message: #{error_message}")

              socket =
                socket
                |> assign(:alert_message, timestamped_message)
                |> assign(:alert_type, :error)

              {:noreply, socket}
          end

        {:error, encoding_error} ->
          timestamped_message =
            add_timestamp_to_message("Payload encoding failed: #{encoding_error}")

          socket =
            socket
            |> assign(:alert_message, timestamped_message)
            |> assign(:alert_type, :error)

          {:noreply, socket}
      end
    else
      # Validation failed - check what specifically failed
      error_message =
        cond do
          client_id == "" ->
            "Please select a client"

          topic == "" ->
            "Please enter a topic"

          payload_error != nil ->
            payload_error

          validated_form["payload_validation_error"] != nil ->
            "Payload validation failed: #{validated_form["payload_validation_error"]}"

          true ->
            "Please fill in all required fields"
        end

      timestamped_message = add_timestamp_to_message(error_message)

      socket =
        socket
        |> assign(:alert_message, timestamped_message)
        |> assign(:alert_type, :error)

      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("select_client", %{"client_id" => client_id} = _params, socket) do
    # Update the client_id in the form
    updated_form = Map.put(socket.assigns.publish_form, "client_id", client_id)

    # Clean and notify parent component about form state change
    cleaned_form = clean_form_for_storage(updated_form)
    broker_name = socket.assigns[:stored_broker_name]
    send(self(), {:update_send_modal_form, cleaned_form, broker_name})

    {:noreply, assign(socket, :publish_form, updated_form)}
  end

  @impl true
  def handle_event("dismiss_alert", _params, socket) do
    socket =
      socket
      |> assign(:alert_message, nil)
      |> assign(:alert_type, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_file", _params, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:publish_form, fn form ->
        form
        |> Map.put("payload", "")
        |> Map.put("payload_format", "text")
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> update(:publish_form, fn form ->
        form
        |> Map.put("payload_format", format)
        |> Map.put("payload", if(format == "file", do: "", else: form["payload"]))
      end)

    {:noreply, socket}
  end

  @impl true
  def handle_event("validate", _params, socket) do
    # Just validate the form, don't process files yet
    {:noreply, socket}
  end

  @impl true
  def handle_event("upload_file", _params, socket) do
    # Process uploaded files when form is submitted
    uploaded_files =
      consume_uploaded_entries(socket, :file, fn %{path: path}, entry ->
        # Read file content and convert to base64
        case File.read(path) do
          {:ok, content} ->
            base64_content = Base.encode64(content)

            file_info = %{
              name: entry.client_name,
              size: entry.client_size,
              type: entry.client_type,
              content: base64_content
            }

            file_info

          {:error, reason} ->
            {:error, "Failed to read file: #{reason}"}
        end
      end)

    case uploaded_files do
      [file_info] when is_map(file_info) ->
        # Update form with file content
        updated_form =
          socket.assigns.publish_form
          |> Map.put("payload", file_info.content)
          |> Map.put("payload_format", "file")

        socket =
          socket
          |> assign(:uploaded_file, file_info)
          |> assign(:publish_form, updated_form)
          |> assign(:alert_message, nil)
          |> assign(:alert_type, nil)

        {:noreply, socket}

      [{:error, error_message}] ->
        # Handle file upload error
        socket =
          socket
          |> assign(
            :alert_message,
            add_timestamp_to_message("File upload failed: #{error_message}")
          )
          |> assign(:alert_type, "error")

        {:noreply, socket}

      [] ->
        # No files uploaded
        socket =
          socket
          |> assign(
            :alert_message,
            add_timestamp_to_message("Please select a file to upload")
          )
          |> assign(:alert_type, "error")

        {:noreply, socket}
    end
  end

  # Handle info messages

  # UI Components

  attr :form, :map, required: true
  attr :myself, :any, required: true
  attr :uploads, :map, required: true
  attr :uploaded_file, :map, default: nil
  attr :label, :string, default: "Payload"

  def payload_input_with_file_upload(assigns) do
    ~H"""
    <div class="form-control w-full">
      <label class="label">
        <span class="label-text font-medium">{@label}</span>
      </label>
      
    <!-- Format Selection -->
      <div class="flex gap-2 mb-3">
        <label class="label cursor-pointer">
          <input
            type="radio"
            name="payload_format"
            value="text"
            checked={@form["payload_format"] == "text" || @form["payload_format"] == nil}
            class="radio radio-primary radio-sm mr-2"
            phx-click="format_changed"
            phx-value-format="text"
            phx-target={@myself}
          />
          <span class="label-text">Text</span>
        </label>
        <label class="label cursor-pointer">
          <input
            type="radio"
            name="payload_format"
            value="json"
            checked={@form["payload_format"] == "json"}
            class="radio radio-primary radio-sm mr-2"
            phx-click="format_changed"
            phx-value-format="json"
            phx-target={@myself}
          />
          <span class="label-text">JSON</span>
        </label>
        <label class="label cursor-pointer">
          <input
            type="radio"
            name="payload_format"
            value="hex"
            checked={@form["payload_format"] == "hex"}
            class="radio radio-primary radio-sm mr-2"
            phx-click="format_changed"
            phx-value-format="hex"
            phx-target={@myself}
          />
          <span class="label-text">Hex</span>
        </label>
        <label class="label cursor-pointer">
          <input
            type="radio"
            name="payload_format"
            value="file"
            checked={@form["payload_format"] == "file"}
            class="radio radio-primary radio-sm mr-2"
            phx-click="format_changed"
            phx-value-format="file"
            phx-target={@myself}
          />
          <span class="label-text">File</span>
        </label>
      </div>
      
    <!-- Payload Input based on format -->
      <%= if @form["payload_format"] == "file" do %>
        <!-- File Upload Interface -->
        <div class="border-2 border-dashed border-base-300 rounded-lg p-6 text-center">
          <%= if @uploaded_file do %>
            <!-- File Information Display -->
            <div class="space-y-4">
              <div class="flex items-center justify-center gap-2 text-success">
                <.icon name="hero-document-check" class="size-6" />
                <span class="font-medium">File uploaded successfully</span>
              </div>

              <div class="text-sm text-base-content/70">
                <p><strong>Name:</strong> {@uploaded_file.name}</p>
                <p><strong>Size:</strong> {format_file_size(@uploaded_file.size)}</p>
                <p><strong>Type:</strong> {@uploaded_file.type}</p>
              </div>

              <button
                type="button"
                class="btn btn-sm btn-outline"
                phx-click="clear_file"
                phx-target={@myself}
              >
                <.icon name="hero-x-mark" class="size-4 mr-1" /> Clear File
              </button>
            </div>
          <% else %>
            <!-- File Upload Input -->
            <div class="space-y-4">
              <.icon name="hero-cloud-arrow-up" class="size-12 mx-auto text-base-content/40" />
              <div>
                <p class="text-lg font-medium">Drop files here or click to browse</p>
                <p class="text-sm text-base-content/60 mt-1">
                  Supports all file types. Files will be converted to base64 for MQTT transmission.
                </p>
              </div>

              <form phx-submit="upload_file" phx-change="validate" phx-target={@myself}>
                <.live_file_input
                  upload={@uploads.file}
                  class="file-input file-input-bordered file-input-primary w-full max-w-xs"
                />
                <button type="submit" class="btn btn-primary btn-sm mt-2">Upload</button>
              </form>
            </div>
          <% end %>
        </div>
      <% else %>
        <!-- Text/JSON/Hex Input -->
        <textarea
          name="payload"
          value={@form["payload"]}
          placeholder={get_payload_placeholder(@form["payload_format"])}
          class="textarea textarea-bordered w-full min-h-[120px]"
          spellcheck="false"
        ><%= @form["payload"] %></textarea>
      <% end %>
      
    <!-- Validation Error -->
      <%= if @form["payload_validation_error"] do %>
        <div class="label">
          <span class="label-text-alt text-error">{@form["payload_validation_error"]}</span>
        </div>
      <% end %>
    </div>
    """
  end

  # Helper Functions

  defp get_payload_placeholder(format) do
    case format do
      "json" -> "Enter JSON payload..."
      "hex" -> "Enter hex payload (e.g., 48656c6c6f)"
      "file" -> ""
      _ -> "Enter payload..."
    end
  end

  defp format_file_size(size) when is_integer(size) do
    cond do
      size >= 1_048_576 -> "#{Float.round(size / 1_048_576, 1)} MB"
      size >= 1024 -> "#{Float.round(size / 1024, 1)} KB"
      true -> "#{size} B"
    end
  end

  defp format_file_size(_), do: "Unknown size"

  defp show_mqtt5_properties?(client_id, active_broker_name) do
    # Show MQTT 5.0 properties if client supports MQTT 5.0
    if client_id != "" do
      connected_clients = get_connected_clients(active_broker_name || "")

      case Enum.find(connected_clients, fn client -> client.client_id == client_id end) do
        %{mqtt_version: version} when version in ["5.0", "5"] -> true
        _ -> false
      end
    else
      false
    end
  end

  defp validate_payload_in_form(form) do
    payload = Map.get(form, "payload", "")
    format = Map.get(form, "payload_format", "text")

    case validate_payload(payload, format) do
      {:ok, _} ->
        Map.put(form, "payload_validation_error", nil)

      {:error, error_message} ->
        Map.put(form, "payload_validation_error", error_message)
    end
  end

  defp validate_payload_in_form_after_template(form, final_payload, payload_format) do
    case validate_payload(final_payload, payload_format) do
      {:ok, _} ->
        Map.put(form, "payload_validation_error", nil)

      {:error, error_message} ->
        Map.put(form, "payload_validation_error", error_message)
    end
  end

  defp validate_payload(payload, format) do
    case format do
      "json" -> validate_json_payload(payload)
      "hex" -> validate_hex_payload(payload)
      "file" -> validate_file_payload(payload)
      "text" -> {:ok, payload}
      _ -> {:ok, payload}
    end
  end

  defp validate_file_payload(""),
    do: {:error, "Please upload a file or select a different format"}

  defp validate_file_payload(payload) when is_binary(payload) do
    # Check if payload looks like base64 (basic validation)
    if String.length(payload) > 0 and String.match?(payload, ~r/^[A-Za-z0-9+\/]*={0,2}$/) do
      case Base.decode64(payload) do
        {:ok, _decoded} -> {:ok, payload}
        :error -> {:error, "Invalid file content. Please upload the file again."}
      end
    else
      {:error, "Please upload a file or select a different format"}
    end
  end

  defp validate_json_payload(""), do: {:ok, ""}

  defp validate_json_payload(payload) when is_binary(payload) do
    case Jason.decode(payload) do
      {:ok, _} -> {:ok, payload}
      {:error, _} -> {:error, "Invalid JSON format"}
    end
  end

  defp validate_hex_payload(""), do: {:ok, ""}

  defp validate_hex_payload(payload) when is_binary(payload) do
    # Remove whitespace and check for valid hex
    cleaned = String.replace(payload, ~r/\s/, "")

    cond do
      cleaned == "" ->
        {:ok, payload}

      not String.match?(cleaned, ~r/^[0-9A-Fa-f]*$/) ->
        {:error, "Invalid hex format. Use only 0-9, A-F characters"}

      rem(String.length(cleaned), 2) != 0 ->
        {:error, "Hex payload must have even number of characters"}

      true ->
        {:ok, payload}
    end
  end

  defp encode_payload_for_transmission(payload, format) do
    case format do
      "hex" ->
        encode_hex_payload(payload)

      "file" ->
        # File payload is base64 encoded, decode it to binary for MQTT transmission
        case Base.decode64(payload) do
          {:ok, binary} -> {:ok, binary}
          :error -> {:error, "Failed to decode file payload"}
        end

      _ ->
        {:ok, payload}
    end
  end

  defp encode_hex_payload(""), do: {:ok, ""}

  defp encode_hex_payload(payload) when is_binary(payload) do
    # Remove whitespace and decode hex to binary
    cleaned = String.replace(payload, ~r/\s/, "")

    case Base.decode16(cleaned, case: :mixed) do
      {:ok, binary} -> {:ok, binary}
      :error -> {:error, "Failed to decode hex payload"}
    end
  end

  defp clean_form_for_storage(form_state) when is_map(form_state) do
    # Remove temporary form fields and Phoenix internal fields that shouldn't be persisted
    cleaned_form =
      form_state
      |> Enum.reject(fn {key, _value} ->
        # Convert key to string to handle both atom and string keys
        key_str = to_string(key)

        String.starts_with?(key_str, "user_property_key_") or
          String.starts_with?(key_str, "user_property_value_") or
          String.starts_with?(key_str, "_target") or
          String.starts_with?(key_str, "_unused_")
      end)
      |> Enum.into(%{}, fn {key, value} ->
        # Ensure all keys are strings for consistency
        {to_string(key), value}
      end)

    # Filter out empty user properties to prevent empty property boxes on page refresh
    user_properties = Map.get(cleaned_form, "user_properties", [])

    filtered_user_properties =
      Enum.filter(user_properties, fn property ->
        key = Map.get(property, "key", "")
        value = Map.get(property, "value", "")
        # Keep property only if at least key or value is not empty
        key != "" || value != ""
      end)

    Map.put(cleaned_form, "user_properties", filtered_user_properties)
  end

  defp default_publish_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload" => "",
      "payload_format" => "text",
      "payload_validation_error" => nil,
      "qos" => "0",
      "retain" => false,
      # MQTT 5.0 properties - use proper data types
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => 0,
      "topic_alias" => 0,
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => []
    }
  end

  defp update_form_with_params(current_form, params) do
    # Update form with all parameters, handling type conversions
    updated_form =
      Enum.reduce(params, current_form, fn {key, value}, acc ->
        case key do
          "qos" ->
            # Keep QoS as string for UI consistency
            Map.put(acc, key, value)

          "retain" ->
            Map.put(acc, key, value == "on")

          "payload_format_indicator" ->
            Map.put(acc, key, value == "on")

          "message_expiry_interval" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 -> Map.put(acc, key, int_val)
                  _ -> Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          "topic_alias" ->
            # Convert to integer, default to 0 if empty or invalid
            case value do
              "" ->
                Map.put(acc, key, 0)

              val when is_binary(val) ->
                case Integer.parse(val) do
                  {int_val, ""} when int_val >= 0 and int_val <= 65535 ->
                    Map.put(acc, key, int_val)

                  _ ->
                    Map.put(acc, key, 0)
                end

              val when is_integer(val) ->
                Map.put(acc, key, val)

              _ ->
                Map.put(acc, key, 0)
            end

          _ ->
            Map.put(acc, key, value)
        end
      end)

    # Handle user properties separately if they exist in params
    user_properties = extract_user_properties_from_params(params)

    if length(user_properties) > 0 do
      Map.put(updated_form, "user_properties", user_properties)
    else
      updated_form
    end
  end

  defp parse_user_properties_from_params(params, current_properties) do
    # Extract user property fields from params and update current properties
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.reduce(current_properties, fn {param_key, value}, acc ->
      case extract_index_and_field(to_string(param_key)) do
        {index, field} when index < length(acc) ->
          List.update_at(acc, index, fn property ->
            Map.put(property, field, value)
          end)

        _ ->
          acc
      end
    end)
  end

  defp extract_index_and_field(param_key) do
    # Extract index and field from keys like "user_property_key_0" or "user_property_value_0"
    case String.split(param_key, "_") do
      ["user", "property", field, index_str] ->
        case Integer.parse(index_str) do
          {index, ""} -> {index, field}
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp extract_user_properties_from_params(params) do
    # Extract user properties from form params
    params
    |> Enum.filter(fn {key, _value} ->
      key_str = to_string(key)
      String.starts_with?(key_str, "user_property_")
    end)
    |> Enum.group_by(fn {key, _value} ->
      # Extract index from key like "user_property_key_0" or "user_property_value_0"
      key_str = to_string(key)

      key_str
      |> String.split("_")
      |> List.last()
      |> String.to_integer()
    end)
    |> Enum.sort_by(fn {index, _} -> index end)
    |> Enum.map(fn {_index, properties} ->
      # Convert list of key-value pairs to a map
      Enum.reduce(properties, %{"key" => "", "value" => ""}, fn {param_key, value}, acc ->
        param_key_str = to_string(param_key)

        if String.contains?(param_key_str, "_key_") do
          Map.put(acc, "key", value)
        else
          Map.put(acc, "value", value)
        end
      end)
    end)
  end

  defp smart_client_selection(form, active_broker_name) do
    connected_clients = get_connected_clients(active_broker_name || "")
    current_client_id = form["client_id"]

    # Check if current client is still valid and connected
    client_still_connected =
      Enum.any?(connected_clients, fn client ->
        client.client_id == current_client_id
      end)

    # If no client selected or current client disconnected, select first available
    if current_client_id == "" || current_client_id == nil || !client_still_connected do
      case connected_clients do
        [first_client | _] ->
          Logger.debug(
            "Smart client selection: selecting #{first_client.client_id} (MQTT #{first_client.mqtt_version || "5.0"})"
          )

          Map.put(form, "client_id", first_client.client_id)

        [] ->
          Logger.debug("Smart client selection: no connected clients available")
          form
      end
    else
      Logger.debug("Smart client selection: keeping current client #{current_client_id}")
      form
    end
  end

  defp get_connected_clients(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get broker-specific client IDs
    broker_client_ids = get_broker_client_ids(broker_name)

    # Get all connected clients
    all_connected_clients = Mqttable.MqttClient.Manager.get_connected_clients()

    # Filter to only include clients that belong to this broker
    all_connected_clients
    |> Enum.filter(fn client -> client.client_id in broker_client_ids end)
  end

  defp get_connected_clients(_broker_name) do
    # If no broker name provided, return empty list
    []
  end

  defp get_broker_client_ids(broker_name) do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        []

      broker ->
        # Extract client IDs from connections in this broker
        broker
        |> Map.get(:connections, [])
        |> Enum.map(fn conn -> Map.get(conn, :client_id) end)
        |> Enum.filter(&(&1 != nil && &1 != ""))
        |> Enum.sort()
    end
  end

  defp build_mqtt5_publish_properties(params, connected_clients, client_id) do
    # Check if client supports MQTT 5.0
    client = Enum.find(connected_clients, fn c -> c.client_id == client_id end)

    case client do
      %{mqtt_version: version} when version in ["5.0", "5"] ->
        # Build MQTT 5.0 properties map
        properties = %{}

        # Add content type if provided
        properties =
          if params["content_type"] && params["content_type"] != "" do
            Map.put(properties, :"Content-Type", params["content_type"])
          else
            properties
          end

        # Add payload format indicator
        properties =
          if params["payload_format_indicator"] == "on" do
            Map.put(properties, :"Payload-Format-Indicator", 1)
          else
            properties
          end

        # Add message expiry interval
        properties =
          if params["message_expiry_interval"] && params["message_expiry_interval"] != "" do
            case Integer.parse(params["message_expiry_interval"]) do
              {interval, ""} when interval >= 0 ->
                Map.put(properties, :"Message-Expiry-Interval", interval)

              _ ->
                properties
            end
          else
            properties
          end

        # Add topic alias
        properties =
          if params["topic_alias"] && params["topic_alias"] != "" do
            case Integer.parse(params["topic_alias"]) do
              {alias, ""} when alias >= 0 and alias <= 65535 ->
                # Only add topic alias if it's greater than 0 (0 means no alias)
                if alias > 0 do
                  Map.put(properties, :"Topic-Alias", alias)
                else
                  properties
                end

              _ ->
                properties
            end
          else
            properties
          end

        # Add response topic
        properties =
          if params["response_topic"] && params["response_topic"] != "" do
            Map.put(properties, :"Response-Topic", params["response_topic"])
          else
            properties
          end

        # Add correlation data
        properties =
          if params["correlation_data"] && params["correlation_data"] != "" do
            Map.put(properties, :"Correlation-Data", params["correlation_data"])
          else
            properties
          end

        # Add user properties
        user_properties = extract_user_properties_from_params(params)

        valid_user_properties =
          Enum.filter(user_properties, fn %{"key" => key, "value" => value} ->
            key != "" && value != ""
          end)

        properties =
          if length(valid_user_properties) > 0 do
            user_props_list =
              Enum.map(valid_user_properties, fn %{"key" => key, "value" => value} ->
                {key, value}
              end)

            Map.put(properties, :"User-Property", user_props_list)
          else
            properties
          end

        properties

      _ ->
        # Client doesn't support MQTT 5.0 or not found
        %{}
    end
  end

  defp get_mqtt5_properties_collapsed_state(broker_name) do
    if broker_name do
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
      Map.get(mqtt5_collapsed_states, broker_name, false)
    else
      false
    end
  end

  defp save_mqtt5_properties_collapsed_state(broker_name, collapsed) do
    ui_state = Mqttable.ConnectionSets.get_ui_state()
    mqtt5_collapsed_states = Map.get(ui_state, :mqtt5_properties_collapsed, %{})
    updated_mqtt5_collapsed_states = Map.put(mqtt5_collapsed_states, broker_name, collapsed)

    updated_ui_state =
      Map.put(ui_state, :mqtt5_properties_collapsed, updated_mqtt5_collapsed_states)

    Mqttable.ConnectionSets.update_ui_state(updated_ui_state)
  end

  defp process_payload(payload, broker_name) do
    # New simplified payload processing
    # Check if payload contains template syntax and render if needed
    if has_template_syntax?(payload) do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(broker_name)

      case Mqttable.Templating.Engine.render(payload, %{}, variables) do
        {:ok, rendered_payload} ->
          {rendered_payload, nil}

        {:error, reason} ->
          {payload, "Template error: #{reason}"}
      end
    else
      # Use payload as-is for plain text
      {payload, nil}
    end
  end

  defp has_template_syntax?(content) when is_binary(content) do
    String.contains?(content, "{{") || String.contains?(content, "{%")
  end

  defp has_template_syntax?(_), do: false

  defp get_broker_variables(broker_name) when is_binary(broker_name) and broker_name != "" do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        %{}

      broker ->
        # Extract enabled variables and convert to map
        broker
        |> Map.get(:variables, [])
        |> Enum.filter(fn var -> Map.get(var, :enabled, true) end)
        |> Enum.reduce(%{}, fn var, acc ->
          name = Map.get(var, :name)
          value = Map.get(var, :value, "")

          if name && name != "" do
            Map.put(acc, name, value)
          else
            acc
          end
        end)
    end
  end

  defp get_broker_variables(_broker_name) do
    %{}
  end

  # Format publish result based on the type of response
  defp format_publish_result(packet_info) do
    case packet_info do
      # QoS 0 messages return simple integer packet_id (usually 0)
      packet_id when is_integer(packet_id) ->
        {"Message sent successfully (Packet ID: #{packet_id})", :info}

      # QoS 1/2 messages return a map with packet_id and properties
      %{packet_id: packet_id, properties: properties, reason_codes: reason_codes}
      when is_map(properties) and is_list(reason_codes) ->
        # Check if there are any reason codes indicating issues
        case reason_codes do
          [] ->
            # No reason codes, successful
            {"Message sent successfully (Packet ID: #{packet_id})", :info}

          [reason_code | _] when is_integer(reason_code) ->
            # Handle reason codes
            case reason_code do
              # Success codes
              0 ->
                {"Message sent successfully (Packet ID: #{packet_id})", :info}

              # Error codes (16 and above are typically errors)
              code when code >= 16 ->
                reason_name = Map.get(properties, :"Reason-String", "Unknown error")
                reason_text = format_reason_code_name(reason_name)
                {"Message send failed: #{reason_text} (Code: #{code})", :error}

              # Warning codes (informational but not necessarily errors)
              code when code in [1, 2, 3, 4] ->
                # Informational codes
                reason_name = Map.get(properties, :"Reason-String", "Unknown")
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

              code ->
                # Other unknown codes
                reason_name = Map.get(properties, :"Reason-String", "Unknown")
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
            end

          [%{reason_code: reason_code, reason_name: reason_name} | _] ->
            # Handle structured reason codes
            case reason_code do
              # Success codes
              0 ->
                {"Message sent successfully (Packet ID: #{packet_id})", :info}

              # Error codes (16 and above are typically errors)
              code when code >= 16 ->
                reason_text = format_reason_code_name(reason_name)
                {"Message send failed: #{reason_text} (Code: #{code})", :error}

              # Warning codes (informational but not necessarily errors)
              code when code in [1, 2, 3, 4] ->
                # Informational codes
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

              code ->
                # Other unknown codes
                reason_text = format_reason_code_name(reason_name)
                {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
            end
        end

      # Fallback for unexpected format
      other ->
        {"Message sent (Response: #{inspect(other)})", :info}
    end
  end

  # Format reason code name for display
  defp format_reason_code_name(reason_name) when is_atom(reason_name) do
    reason_name
    |> Atom.to_string()
    |> String.replace("_", " ")
    |> String.capitalize()
  end

  defp format_reason_code_name(reason_name) when is_binary(reason_name) do
    reason_name
  end

  defp format_reason_code_name(_), do: "Unknown"

  # Add RFC3339 timestamp to alert message
  defp add_timestamp_to_message(message) do
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601()
    "[#{timestamp}] #{message}"
  end
end
