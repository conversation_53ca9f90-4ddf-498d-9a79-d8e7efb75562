// File Upload Hook for Phoenix LiveView
// Handles file selection and converts to base64 for MQTT transmission

const FileUpload = {
  mounted() {
    this.el.addEventListener('change', (event) => {
      const file = event.target.files[0];
      
      if (!file) {
        return;
      }

      // Check file size (16MB limit)
      const maxSize = 16 * 1024 * 1024; // 16MB
      if (file.size > maxSize) {
        this.pushEvent("file_upload_error", {
          error: `File too large. Maximum size is 16MB, but file is ${this.formatFileSize(file.size)}.`
        });
        return;
      }

      // Read file and convert to base64
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          // Get base64 content (remove data:mime;base64, prefix)
          const base64Content = e.target.result.split(',')[1];
          
          const fileData = {
            name: file.name,
            size: file.size,
            type: file.type,
            content: base64Content
          };

          // Send file data to LiveView
          this.pushEvent("file_uploaded", { file_data: fileData });
          
        } catch (error) {
          console.error('Error processing file:', error);
          this.pushEvent("file_upload_error", {
            error: "Failed to process file. Please try again."
          });
        }
      };

      reader.onerror = () => {
        this.pushEvent("file_upload_error", {
          error: "Failed to read file. Please try again."
        });
      };

      // Read file as data URL (base64)
      reader.readAsDataURL(file);
    });
  },

  // Helper function to format file size
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};

export default FileUpload;
