// File Upload Hook for Phoenix LiveView
// Handles file selection and converts to base64, then updates payload input directly

const FileUpload = {
  mounted() {
    this.el.addEventListener('change', (event) => {
      const file = event.target.files[0];

      if (!file) {
        return;
      }

      // Check file size (16MB limit)
      const maxSize = 16 * 1024 * 1024; // 16MB
      if (file.size > maxSize) {
        alert(`File too large. Maximum size is 16MB, but file is ${this.formatFileSize(file.size)}.`);
        this.el.value = ''; // Clear the file input
        return;
      }

      // Read file and convert to base64
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          // Get base64 content (remove data:mime;base64, prefix)
          const base64Content = e.target.result.split(',')[1];

          // Find the payload input field and update it
          const payloadInput = document.querySelector('textarea[name="publish[payload]"]') ||
                              document.querySelector('input[name="publish[payload]"]') ||
                              document.querySelector('textarea[name="scheduled_message[payload]"]') ||
                              document.querySelector('input[name="scheduled_message[payload]"]');

          if (payloadInput) {
            payloadInput.value = base64Content;

            // Trigger change event to notify LiveView
            const changeEvent = new Event('input', { bubbles: true });
            payloadInput.dispatchEvent(changeEvent);

            // Show file info (optional)
            console.log(`File uploaded: ${file.name} (${this.formatFileSize(file.size)})`);

            // You could also update a file info display element if it exists
            const fileInfoElement = document.querySelector('.file-info-display');
            if (fileInfoElement) {
              fileInfoElement.innerHTML = `
                <div class="text-sm text-base-content/70">
                  <p><strong>File:</strong> ${file.name}</p>
                  <p><strong>Size:</strong> ${this.formatFileSize(file.size)}</p>
                  <p><strong>Type:</strong> ${file.type}</p>
                </div>
              `;
              fileInfoElement.style.display = 'block';
            }
          } else {
            console.error('Could not find payload input field');
            alert('Error: Could not find payload input field');
          }

        } catch (error) {
          console.error('Error processing file:', error);
          alert('Failed to process file. Please try again.');
          this.el.value = ''; // Clear the file input
        }
      };

      reader.onerror = () => {
        alert('Failed to read file. Please try again.');
        this.el.value = ''; // Clear the file input
      };

      // Read file as data URL (base64)
      reader.readAsDataURL(file);
    });
  },

  // Helper function to format file size
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};

export default FileUpload;
